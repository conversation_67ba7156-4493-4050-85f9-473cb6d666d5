<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快乐8数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .number-badge {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .hot-number {
            background: #dc3545;
        }
        .cold-number {
            background: #6c757d;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .btn-refresh {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
        }
        .btn-refresh:hover {
            background: linear-gradient(135deg, #43a3f5 0%, #00e8f5 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                快乐8数据分析系统
            </a>
            <button class="btn btn-refresh" onclick="refreshData()">
                <i class="fas fa-sync-alt me-1"></i>
                刷新数据
            </button>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 基本统计信息 -->
        <div class="row" id="statsSection">
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <h3 id="totalPeriods">-</h3>
                    <p>总期数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <h3 id="dateRange">-</h3>
                    <p>数据跨度(天)</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <h3 id="latestPeriod">-</h3>
                    <p>最新期号</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <h3 id="earliestPeriod">-</h3>
                    <p>最早期号</p>
                </div>
            </div>
        </div>

        <!-- 最新开奖结果 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-trophy me-2"></i>
                最新开奖结果
            </div>
            <div class="card-body">
                <div id="latestResults" class="loading">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row">
            <!-- 号码频率图表 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-bar me-2"></i>
                        号码出现频率 TOP 20
                    </div>
                    <div class="card-body">
                        <div id="frequencyChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>

            <!-- 最近趋势图表 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-line me-2"></i>
                        最近50期热门号码
                    </div>
                    <div class="card-body">
                        <div id="trendChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 号码数量分布 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-pie me-2"></i>
                        每期号码数量分布
                    </div>
                    <div class="card-body">
                        <div id="distributionChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>

            <!-- 号码热力图 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-fire me-2"></i>
                        号码频率热力图
                    </div>
                    <div class="card-body">
                        <div id="heatmapChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-table me-2"></i>
                号码频率详细数据
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>最热门号码 TOP 20</h6>
                        <div id="hotNumbers"></div>
                    </div>
                    <div class="col-md-6">
                        <h6>最近50期热门号码 TOP 15</h6>
                        <div id="recentHotNumbers"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                <i class="fas fa-info-circle me-1"></i>
                数据仅供参考，彩票具有随机性，请理性购彩
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
        });

        // 加载所有数据
        async function loadAllData() {
            try {
                await Promise.all([
                    loadBasicStats(),
                    loadLatestResults(),
                    loadFrequencyChart(),
                    loadTrendChart(),
                    loadDistributionChart(),
                    loadHeatmapChart(),
                    loadFrequencyData()
                ]);
            } catch (error) {
                console.error('加载数据失败:', error);
                showError('数据加载失败，请检查服务器连接');
            }
        }

        // 加载基本统计信息
        async function loadBasicStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                document.getElementById('totalPeriods').textContent = data.total_periods;
                document.getElementById('dateRange').textContent = data.date_range.days;
                document.getElementById('latestPeriod').textContent = data.latest_period;
                document.getElementById('earliestPeriod').textContent = data.earliest_period;
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载最新开奖结果
        async function loadLatestResults() {
            try {
                const response = await fetch('/api/results?limit=5');
                const data = await response.json();
                
                let html = '';
                data.forEach(result => {
                    html += `
                        <div class="mb-3 p-3 border rounded">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>期号:</strong> ${result.period}<br>
                                    <strong>日期:</strong> ${result.date}
                                </div>
                                <div class="col-md-9">
                                    <strong>开奖号码:</strong><br>
                                    ${result.numbers.map(num => `<span class="number-badge">${num.toString().padStart(2, '0')}</span>`).join('')}
                                    <small class="text-muted ms-2">(共${result.count}个号码)</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                document.getElementById('latestResults').innerHTML = html;
            } catch (error) {
                console.error('加载开奖结果失败:', error);
                document.getElementById('latestResults').innerHTML = '<p class="text-danger">加载失败</p>';
            }
        }

        // 加载频率图表
        async function loadFrequencyChart() {
            try {
                const response = await fetch('/api/charts/frequency?top_n=20');
                const data = await response.json();
                const chartData = JSON.parse(data.chart);
                
                Plotly.newPlot('frequencyChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('加载频率图表失败:', error);
            }
        }

        // 加载趋势图表
        async function loadTrendChart() {
            try {
                const response = await fetch('/api/charts/trends?periods=50');
                const data = await response.json();
                const chartData = JSON.parse(data.chart);
                
                Plotly.newPlot('trendChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('加载趋势图表失败:', error);
            }
        }

        // 加载分布图表
        async function loadDistributionChart() {
            try {
                const response = await fetch('/api/charts/distribution');
                const data = await response.json();
                const chartData = JSON.parse(data.chart);
                
                Plotly.newPlot('distributionChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('加载分布图表失败:', error);
            }
        }

        // 加载热力图
        async function loadHeatmapChart() {
            try {
                const response = await fetch('/api/charts/heatmap');
                const data = await response.json();
                const chartData = JSON.parse(data.chart);
                
                Plotly.newPlot('heatmapChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('加载热力图失败:', error);
            }
        }

        // 加载频率数据
        async function loadFrequencyData() {
            try {
                // 加载总体频率数据
                const freqResponse = await fetch('/api/frequency?limit=20');
                const freqData = await freqResponse.json();
                
                let hotHtml = '';
                freqData.forEach(item => {
                    hotHtml += `<span class="number-badge hot-number">${item.number.toString().padStart(2, '0')} (${item.frequency}%)</span>`;
                });
                document.getElementById('hotNumbers').innerHTML = hotHtml;
                
                // 加载最近趋势数据
                const trendResponse = await fetch('/api/trends?periods=50');
                const trendData = await trendResponse.json();
                
                let recentHtml = '';
                trendData.slice(0, 15).forEach(item => {
                    recentHtml += `<span class="number-badge">${item.number.toString().padStart(2, '0')} (${item.frequency}%)</span>`;
                });
                document.getElementById('recentHotNumbers').innerHTML = recentHtml;
                
            } catch (error) {
                console.error('加载频率数据失败:', error);
            }
        }

        // 刷新数据
        async function refreshData() {
            const btn = document.querySelector('.btn-refresh');
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
            btn.disabled = true;
            
            try {
                const response = await fetch('/api/refresh', {method: 'POST'});
                const result = await response.json();
                
                if (result.success) {
                    showSuccess('数据刷新成功！');
                    // 重新加载所有数据
                    await loadAllData();
                } else {
                    showError('数据刷新失败');
                }
            } catch (error) {
                console.error('刷新数据失败:', error);
                showError('刷新数据失败，请检查网络连接');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }

        // 显示错误消息
        function showError(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }
    </script>
</body>
</html>
