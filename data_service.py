#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8数据服务模块
提供数据分析和处理功能
"""

import pandas as pd
import json
import os
from collections import Counter
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import plotly.graph_objects as go
import plotly.express as px
from plotly.utils import PlotlyJSONEncoder
from functools import lru_cache
import time

class Happy8DataService:
    def __init__(self, data_file: str = 'happy8_data.csv'):
        self.data_file = data_file
        self.df = None
        self._cache = {}
        self._cache_timeout = 300  # 5分钟缓存
        self.load_data()

    def _get_cache_key(self, method_name: str, *args, **kwargs) -> str:
        """生成缓存键"""
        return f"{method_name}_{hash(str(args) + str(sorted(kwargs.items())))}"

    def _get_cached_result(self, cache_key: str):
        """获取缓存结果"""
        if cache_key in self._cache:
            result, timestamp = self._cache[cache_key]
            if time.time() - timestamp < self._cache_timeout:
                return result
            else:
                del self._cache[cache_key]
        return None

    def _set_cache(self, cache_key: str, result):
        """设置缓存"""
        self._cache[cache_key] = (result, time.time())

    def clear_cache(self):
        """清除缓存"""
        self._cache.clear()

    def load_data(self) -> bool:
        """加载开奖数据"""
        try:
            if os.path.exists(self.data_file):
                self.df = pd.read_csv(self.data_file)
                self.df['date'] = pd.to_datetime(self.df['date'])

                # 修复时间顺序：按期号降序排列，最新的在前面
                self.df = self.df.sort_values('period', ascending=False).reset_index(drop=True)

                print(f"成功加载 {len(self.df)} 期开奖数据")
                return True
            else:
                print(f"数据文件 {self.data_file} 不存在")
                return False
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def get_basic_stats(self) -> Dict:
        """获取基本统计信息"""
        if self.df is None:
            return {}

        return {
            'total_periods': int(len(self.df)),
            'date_range': {
                'start': self.df['date'].min().strftime('%Y-%m-%d'),
                'end': self.df['date'].max().strftime('%Y-%m-%d'),
                'days': int((self.df['date'].max() - self.df['date'].min()).days)
            },
            'latest_period': str(self.df.iloc[0]['period']) if len(self.df) > 0 else None,
            'earliest_period': str(self.df.iloc[-1]['period']) if len(self.df) > 0 else None
        }
    
    def get_number_frequency(self, limit: int = 80) -> List[Dict]:
        """获取号码出现频率"""
        if self.df is None:
            return []
        
        all_numbers = []
        for _, row in self.df.iterrows():
            numbers = eval(row['numbers'])
            all_numbers.extend([int(num) for num in numbers])
        
        frequency = Counter(all_numbers)
        total_periods = len(self.df)
        
        result = []
        for num, count in frequency.most_common(limit):
            result.append({
                'number': num,
                'count': count,
                'frequency': round((count / total_periods) * 100, 2)
            })
        
        return result
    
    def get_recent_trends(self, periods: int = 50) -> List[Dict]:
        """获取最近趋势（现在数据已按期号降序排列，head()获取的就是最新数据）"""
        if self.df is None:
            return []

        # 获取最近的periods期数据（现在已经是按期号降序排列）
        recent_df = self.df.head(periods)
        recent_numbers = []

        for _, row in recent_df.iterrows():
            numbers = eval(row['numbers'])
            recent_numbers.extend([int(num) for num in numbers])

        frequency = Counter(recent_numbers)
        total_periods = len(recent_df)

        result = []
        for num, count in frequency.most_common(20):
            result.append({
                'number': num,
                'count': count,
                'frequency': round((count / total_periods) * 100, 2)
            })

        return result
    
    def get_latest_results(self, limit: int = 10) -> List[Dict]:
        """获取最新开奖结果"""
        if self.df is None:
            return []

        results = []
        for i in range(min(limit, len(self.df))):
            row = self.df.iloc[i]
            numbers = eval(row['numbers'])
            results.append({
                'period': str(row['period']),
                'date': row['date'].strftime('%Y-%m-%d'),
                'numbers': [int(num) for num in numbers],
                'numbers_str': ' '.join([f"{int(num):02d}" for num in numbers]),
                'count': len(numbers)
            })

        return results
    
    def get_number_distribution(self) -> Dict:
        """获取号码数量分布"""
        if self.df is None:
            return {}
        
        numbers_per_period = []
        for _, row in self.df.iterrows():
            numbers = eval(row['numbers'])
            numbers_per_period.append(len(numbers))
        
        distribution = Counter(numbers_per_period)
        
        return {
            'min_count': min(numbers_per_period),
            'max_count': max(numbers_per_period),
            'avg_count': round(sum(numbers_per_period) / len(numbers_per_period), 1),
            'distribution': dict(distribution)
        }
    
    def create_frequency_chart(self, top_n: int = 20) -> str:
        """创建号码频率图表（带缓存）"""
        cache_key = self._get_cache_key('frequency_chart', top_n)
        cached_result = self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        frequency_data = self.get_number_frequency(top_n)

        if not frequency_data:
            return "{}"

        numbers = [f"{item['number']:02d}" for item in frequency_data]
        frequencies = [item['frequency'] for item in frequency_data]

        fig = go.Figure(data=[
            go.Bar(
                x=numbers,
                y=frequencies,
                text=[f"{freq}%" for freq in frequencies],
                textposition='auto',
                marker_color='rgba(55, 128, 191, 0.7)',
                marker_line_color='rgba(55, 128, 191, 1.0)',
                marker_line_width=1.5
            )
        ])

        fig.update_layout(
            title=f'快乐8号码出现频率 TOP {top_n}',
            xaxis_title='号码',
            yaxis_title='出现频率 (%)',
            template='plotly_white',
            height=400
        )

        result = json.dumps(fig, cls=PlotlyJSONEncoder)
        self._set_cache(cache_key, result)
        return result
    
    def create_trend_chart(self, periods: int = 50) -> str:
        """创建最近趋势图表"""
        trend_data = self.get_recent_trends(periods)
        
        if not trend_data:
            return "{}"
        
        numbers = [f"{item['number']:02d}" for item in trend_data[:15]]
        frequencies = [item['frequency'] for item in trend_data[:15]]
        
        fig = go.Figure(data=[
            go.Bar(
                x=numbers,
                y=frequencies,
                text=[f"{freq}%" for freq in frequencies],
                textposition='auto',
                marker_color='rgba(255, 127, 14, 0.7)',
                marker_line_color='rgba(255, 127, 14, 1.0)',
                marker_line_width=1.5
            )
        ])
        
        fig.update_layout(
            title=f'最近{periods}期热门号码 TOP 15',
            xaxis_title='号码',
            yaxis_title='出现频率 (%)',
            template='plotly_white',
            height=400
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def create_distribution_chart(self) -> str:
        """创建号码数量分布图表"""
        dist_data = self.get_number_distribution()
        
        if not dist_data or not dist_data.get('distribution'):
            return "{}"
        
        counts = list(dist_data['distribution'].keys())
        periods = list(dist_data['distribution'].values())
        
        fig = go.Figure(data=[
            go.Bar(
                x=[f"{count}个" for count in counts],
                y=periods,
                text=periods,
                textposition='auto',
                marker_color='rgba(44, 160, 44, 0.7)',
                marker_line_color='rgba(44, 160, 44, 1.0)',
                marker_line_width=1.5
            )
        ])
        
        fig.update_layout(
            title='每期开出号码数量分布',
            xaxis_title='号码数量',
            yaxis_title='期数',
            template='plotly_white',
            height=400
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def create_heatmap_chart(self) -> str:
        """创建号码热力图"""
        if self.df is None:
            return "{}"
        
        # 创建80个号码的矩阵 (8x10)
        frequency_data = self.get_number_frequency(80)
        freq_dict = {item['number']: item['frequency'] for item in frequency_data}
        
        # 创建8x10的矩阵
        matrix = []
        for row in range(8):
            row_data = []
            for col in range(10):
                number = row * 10 + col + 1
                if number <= 80:
                    row_data.append(freq_dict.get(number, 0))
                else:
                    row_data.append(0)
            matrix.append(row_data)
        
        # 创建标签
        x_labels = [f"{i+1:02d}" for i in range(10)]
        y_labels = [f"{i*10+1:02d}-{min((i+1)*10, 80):02d}" for i in range(8)]
        
        fig = go.Figure(data=go.Heatmap(
            z=matrix,
            x=x_labels,
            y=y_labels,
            colorscale='Viridis',
            showscale=True,
            hoverongaps=False
        ))
        
        fig.update_layout(
            title='快乐8号码频率热力图',
            xaxis_title='号码尾数',
            yaxis_title='号码区间',
            template='plotly_white',
            height=500
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def refresh_data(self) -> bool:
        """刷新数据（重新运行爬虫）"""
        try:
            from happy8_crawler import Happy8Crawler
            crawler = Happy8Crawler()
            crawler.run(500)
            # 清除缓存
            self.clear_cache()
            return self.load_data()
        except Exception as e:
            print(f"刷新数据失败: {e}")
            return False
