# 福彩快乐8开奖数据爬虫

这是一个用于爬取福彩快乐8历史开奖数据的Python脚本，可以获取最近500期的开奖结果。

## 功能特点

- 🎯 爬取最近500期快乐8开奖数据
- 📊 支持CSV和JSON两种数据格式输出
- 🔍 包含数据分析功能
- 🛡️ 具备错误处理和重试机制
- 📈 提供号码频率统计和趋势分析

## 文件说明

- `happy8_crawler.py` - 主爬虫脚本
- `analyze_data.py` - 数据分析脚本
- `requirements.txt` - 依赖包列表
- `happy8_data.csv` - 爬取的数据（CSV格式）
- `happy8_data.json` - 爬取的数据（JSON格式）

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 爬取数据

```bash
python happy8_crawler.py
```

这将爬取最近500期的快乐8开奖数据，并保存为CSV和JSON文件。

### 2. 分析数据

```bash
python analyze_data.py
```

这将对爬取到的数据进行分析，包括：
- 号码出现频率统计
- 最热门和最冷门号码
- 最近趋势分析
- 数据时间范围统计

## 数据格式

### CSV格式
```csv
period,numbers,numbers_str,date
2024144,"['01', '04', '05', '07', '08']","01 04 05 07 08",2024-05-23
```

### JSON格式
```json
{
  "period": "2024144",
  "numbers": ["01", "04", "05", "07", "08"],
  "numbers_str": "01 04 05 07 08",
  "date": "2024-05-23"
}
```

## 数据字段说明

- `period`: 期号（7位数字，如2024144）
- `numbers`: 开奖号码列表
- `numbers_str`: 开奖号码字符串（空格分隔）
- `date`: 开奖日期（YYYY-MM-DD格式）

## 爬取结果示例

成功爬取500期数据后，你将看到类似以下的输出：

```
==================================================
福彩快乐8开奖数据爬虫
==================================================
开始爬取最近500期快乐8开奖数据...
页面获取成功，开始解析数据...
找到表格，开始解析...
找到 501 行数据
解析期号: 2024144, 号码: 01 04 05 07 08 09 10 11 13 18 24 27 43 48 62 66 71 75 76 78
...
成功解析 500 期数据
数据已保存到 happy8_data.csv
数据已保存到 happy8_data.json

爬取完成！
总共获取 500 期数据
最新期号: 2024144
最早期号: 2023348
```

## 数据分析结果示例

运行分析脚本后，你将看到：

```
快乐8开奖数据分析
==================================================
成功加载 500 期开奖数据

最热门的20个号码:
号码 02: 出现 223 次, 频率  44.6%
号码 01: 出现 216 次, 频率  43.2%
号码 03: 出现 168 次, 频率  33.6%
...

最近50期最热门的15个号码:
号码 04: 出现 20 次, 频率  40.0%
号码 56: 出现 19 次, 频率  38.0%
...
```

## 技术实现

- **数据源**: 新浪彩票快乐8走势图页面
- **解析方式**: BeautifulSoup HTML解析
- **数据处理**: pandas数据处理
- **输出格式**: CSV和JSON
- **错误处理**: 包含重试机制和异常处理

## 注意事项

1. **数据准确性**: 数据来源于第三方网站，仅供参考
2. **爬取频率**: 建议适当控制爬取频率，避免对服务器造成压力
3. **网络依赖**: 需要稳定的网络连接
4. **页面变化**: 如果源网站页面结构发生变化，可能需要更新解析逻辑
5. **法律合规**: 请确保爬取行为符合相关法律法规和网站使用条款

## 自定义配置

你可以修改 `happy8_crawler.py` 中的参数来自定义爬取行为：

```python
# 修改爬取期数（默认500期）
crawler.run(1000)  # 爬取1000期

# 修改输出文件名
crawler.save_to_csv('my_data.csv')
crawler.save_to_json('my_data.json')
```

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 尝试使用VPN或代理

2. **解析失败**
   - 检查源网站是否可访问
   - 确认页面结构是否发生变化

3. **依赖包错误**
   - 重新安装依赖：`pip install -r requirements.txt`

4. **数据不完整**
   - 检查网络稳定性
   - 重新运行爬虫脚本

## 免责声明

本工具仅用于学习和研究目的，爬取的数据仅供参考。彩票具有随机性，任何基于历史数据的分析都不能保证未来结果。请理性购彩，量力而行。

## 许可证

MIT License

## 更新日志

- v1.0.0: 初始版本，支持爬取500期快乐8数据
- 包含基础数据分析功能
- 支持CSV和JSON输出格式
