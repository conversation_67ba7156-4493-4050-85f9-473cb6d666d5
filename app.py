#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8数据分析Web服务
基于FastAPI的Web应用
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
from typing import List, Dict, Optional
import os
import uvicorn

from data_service import Happy8DataService

# 创建FastAPI应用
app = FastAPI(
    title="快乐8数据分析系统",
    description="福彩快乐8历史开奖数据分析和可视化平台",
    version="1.0.0"
)

# 配置模板和静态文件
templates = Jinja2Templates(directory="templates")

# 创建静态文件目录（如果不存在）
if not os.path.exists("static"):
    os.makedirs("static")

app.mount("/static", StaticFiles(directory="static"), name="static")

# 初始化数据服务
data_service = Happy8DataService()

# 响应模型
class BasicStatsResponse(BaseModel):
    total_periods: int
    date_range: Dict
    latest_period: Optional[str]
    earliest_period: Optional[str]

class NumberFrequencyResponse(BaseModel):
    number: int
    count: int
    frequency: float

class LotteryResultResponse(BaseModel):
    period: str
    date: str
    numbers: List[int]
    numbers_str: str
    count: int

class DistributionResponse(BaseModel):
    min_count: int
    max_count: int
    avg_count: float
    distribution: Dict[str, int]

# 路由定义

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/api/stats", response_model=BasicStatsResponse)
async def get_basic_stats():
    """获取基本统计信息"""
    try:
        stats = data_service.get_basic_stats()
        if not stats:
            raise HTTPException(status_code=404, detail="数据未找到")
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@app.get("/api/frequency", response_model=List[NumberFrequencyResponse])
async def get_number_frequency(limit: int = 20):
    """获取号码出现频率"""
    try:
        frequency = data_service.get_number_frequency(limit)
        return frequency
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取频率数据失败: {str(e)}")

@app.get("/api/trends", response_model=List[NumberFrequencyResponse])
async def get_recent_trends(periods: int = 50):
    """获取最近趋势"""
    try:
        trends = data_service.get_recent_trends(periods)
        return trends
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取趋势数据失败: {str(e)}")

@app.get("/api/results", response_model=List[LotteryResultResponse])
async def get_latest_results(limit: int = 10):
    """获取最新开奖结果"""
    try:
        results = data_service.get_latest_results(limit)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取开奖结果失败: {str(e)}")

@app.get("/api/distribution", response_model=DistributionResponse)
async def get_number_distribution():
    """获取号码数量分布"""
    try:
        distribution = data_service.get_number_distribution()
        if not distribution:
            raise HTTPException(status_code=404, detail="分布数据未找到")
        return distribution
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分布数据失败: {str(e)}")

@app.get("/api/charts/frequency")
async def get_frequency_chart(top_n: int = 20):
    """获取频率图表数据"""
    try:
        chart_data = data_service.create_frequency_chart(top_n)
        return JSONResponse(content={"chart": chart_data})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成频率图表失败: {str(e)}")

@app.get("/api/charts/trends")
async def get_trend_chart(periods: int = 50):
    """获取趋势图表数据"""
    try:
        chart_data = data_service.create_trend_chart(periods)
        return JSONResponse(content={"chart": chart_data})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成趋势图表失败: {str(e)}")

@app.get("/api/charts/distribution")
async def get_distribution_chart():
    """获取分布图表数据"""
    try:
        chart_data = data_service.create_distribution_chart()
        return JSONResponse(content={"chart": chart_data})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成分布图表失败: {str(e)}")

@app.get("/api/charts/heatmap")
async def get_heatmap_chart():
    """获取热力图数据"""
    try:
        chart_data = data_service.create_heatmap_chart()
        return JSONResponse(content={"chart": chart_data})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成热力图失败: {str(e)}")

@app.post("/api/refresh")
async def refresh_data():
    """刷新数据"""
    try:
        success = data_service.refresh_data()
        if success:
            return {"message": "数据刷新成功", "success": True}
        else:
            raise HTTPException(status_code=500, detail="数据刷新失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刷新数据失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "快乐8数据分析系统",
        "version": "1.0.0",
        "data_loaded": data_service.df is not None
    }

# 错误处理
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    return templates.TemplateResponse(
        "error.html", 
        {"request": request, "error": "页面未找到", "status_code": 404}
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: HTTPException):
    return templates.TemplateResponse(
        "error.html", 
        {"request": request, "error": "服务器内部错误", "status_code": 500}
    )

if __name__ == "__main__":
    # 检查数据文件是否存在
    if not os.path.exists("happy8_data.csv"):
        print("警告: 数据文件不存在，请先运行爬虫脚本获取数据")
        print("运行命令: python happy8_crawler.py")
    
    # 启动服务
    print("启动快乐8数据分析Web服务...")
    print("访问地址: http://localhost:8000")
    
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
